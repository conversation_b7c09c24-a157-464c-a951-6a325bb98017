---
name: Accessibility Digital Experience (ADE) Intake Form
about: Use this ticket to request collaboration on a new initiative with the Accessibility
  Digital Experience (ADE) team (pronounced /ād/).
title: "[ADE Intake]:<Team Name><Product><Service Type>"
labels: ADE Intake, ADE
assignees: artsym<PERSON>ha68, lakper, sara-amanda

---

> [!NOTE]
> **QUICK SUMMARY:** `<PERSON><PERSON><PERSON><PERSON><PERSON>DER TEXT for high-level overview`

## About Your Team

- **Product name**: `Product name goes here`
    - [ ] Add your product label to this ticket
- **Team name**: `Team name goes here`
    - [ ] Add your team label to this ticket
- Are you a Veteran Facing Services (VFS) team
    - [ ] Yes
    - [ ] No 
- **OCTO product owner**: `OCTO PO goes here`
- **Product manager**: `PM name goes here`
- **Designer(s)**: `Name of designer(s)`
- **Slack channel**: `Link to channel(s)`
- **Dedicated accessibility specialist on your team** (if you have one):
- **Accessibility Champ** (if you have one):

## About Your Work

### What's the nature of your initiative and desired outcomes?

- `<PERSON><PERSON><PERSON><PERSON>OLDER TEXT for the nature of your initiative`
- `<PERSON><PERSON><PERSON><PERSON><PERSON>DER TEXT for your desired outcomes`
  
## Collaborate with ADE
[**Review our menu of services**,](https://github.com/department-of-veterans-affairs/va.gov-team/blob/master/teams/digital-experience/ADE/readme.md#services-we-offer) and check all the types of collaboration you're requesting, below:

### How would you like to collaborate with ADE?
- [ ] Research support [(if yes, please open an additional research ticket)](https://github.com/department-of-veterans-affairs/va.gov-team/blob/master/.github/ISSUE_TEMPLATE/ade-accessibility-research.yaml)
    - [ ] Discuss research plan / conversation guide / recruitment of AT users
    - [ ] Advise on accessible prototypes
    - [ ] Schedule a [pilot session](https://github.com/department-of-veterans-affairs/va.gov-team/blob/master/teams/digital-experience/ADE/assistive-tech-pilot-guide.md) with an ADE Accessibility Specialist
    - [ ] Schedule technical support during AT research sessions
    - [ ] Document accessibility-related issues during AT research sessions
    - [ ] Conduct research for your team
- [ ] VA 508 office audit
    - [ ] Prepping for a 508 office audit
    - [ ] Reviewing results from a 508 office audit
- [ ] Review Commercial Off the Shelf (COTS) products for accessibility
- [ ] Collaboration Cycle Support
- [ ] Accessibility test planning
- [ ] Support design discovery
- [ ] Review wireframes or mockups
- [ ] PDF audit
- [ ] Staging / CodePen / Codespaces / Production audit
- [ ] Health check audit
- [ ] Training and education
- [ ] Spot check, general guidance
- [ ] **Something else:**  `insert type of collaboration`
- [ ] **Unsure?** _Check this box if you're not sure what kind of support you need, need multiple types of support, and/or want to meet with us. We'll schedule an introductory meeting to learn more about your team and project._


## Supporting information
### Starting URL
(URL)
### Login information (if applicable)
(answer)
### What screens need to be tested?
(answer)
### Are there any custom (non-VADS) components in use? Where?
(answer)
### Are there any conditional fields or screens? Where?
(answer)
### Additional artifacts
Provide links to any supporting artifacts that can help us better understand your initiative and begin collaboration. This could include your project overview, user flow diagrams, etc.
- [Artifact 1](Placeholder1)
- [Artifact 2](Placeholder2)
- [Artifact 3](Placeholder3)

## Your Timeframe

> [!IMPORTANT]
> - We work with the majority of product teams on VA.gov.  Intake requests are prioritized based on overall workload, along with VA and OCTO priorities. While we aim to support all teams who request our services, VFS teams receive priority status due to the nature of their work.  
> - If you have questions about the status of your ticket(s) please reach out to us in the [accessibility-help](https://dsva.slack.com/archives/C8E985R32) Slack channel by tagging Lakwi Perera.


### Is this work tied to a deadline?
- [ ] Yes (Month/Day/Year)
- [ ] No


## Collaboration Cycle - VFS Teams Only

### Ticket Number
`Link to main Collaboration Cycle ticket` (if applicable)

### Which phases of the collaboration cycle have you completed?
_Select all that apply._
- [ ] Kickoff/PO Sync
- [ ] Design intent
- [ ] Midpoint review
- [ ] Staging review



## Next Steps for Your Team
- [ ] Make sure your team and product labels are on this ticket. 
- [ ] Save and submit this intake ticket.
- [ ] Start a new thread, and post a link to this ticket in the [accessibility-help](https://dsva.slack.com/archives/C8E985R32) Slack channel by tagging Lakwi Perera.
- [ ] If you're requesting accessibility support for user research, submit a research support request next: [Open an accessibility research support request](https://github.com/department-of-veterans-affairs/va.gov-team/blob/master/.github/ISSUE_TEMPLATE/ade-accessibility-research.yaml).
- [ ] If you're creating an experimental design, also contact the design system team: [Read more about experimental designs](https://design.va.gov/about/contributing-to-the-design-system/experimental-components-and-patterns#what-is-an-experimental-component-or-pattern?) 
- [ ] [Suggest an addition or update to the design system team](https://design.va.gov/about/contributing-to-the-design-system/suggest-an-addition-or-update)
- [ ] Please don't close this intake when work is complete - ADE will close and track.

## ADE Internal
### Ticket Updates
- [ ] Receive new intake ticket
- [ ] Verify team label has been added
- [ ] Verify product label has been added
- [ ] Respond to Slack message sent by VFS team upon submission
- [ ] Reach out to the VFS team to set up Kickoff (if requested)
- [ ] Reviewed by OCTO/Martha
- [ ] Connect related tickets in this ticket
- [ ] Create child tickets, as they apply to the work requested.
- [ ] Create Quick Summary for top of ticket as a note.
- [ ] _Label with `Originator/Team` (product team or stakeholder requesting support)_
- [ ] _Label date in the `Open Date` field_
- [ ] _Label with `Estimate` (level of effort expected for this ticket)_
- [ ] _Add `Assignee(s)` name(s) to ticket_ (include Lakwi)
- [ ] _Add `Assignee(s) name(s) to each task` they will complete via handle tag (if known)_
- [ ] _Select a `Priority Level`_
- [ ] _Update date in `Last Checked` field_
- [ ] _Label with `Actual` (level of effort it took to complete this ticket)_
- [ ] _Update date in_ `Closed Date`
